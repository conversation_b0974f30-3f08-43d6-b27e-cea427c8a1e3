<template>
  <DocumentEditor v-if="state.config" :id="state.file?.fileId" documentServerUrl="http://************:8060" :config="state.config" />
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import { DocumentEditor } from '@onlyoffice/document-editor-vue';
  import { onlyOfficeConfig } from '@/api/basic/common';

  defineExpose({ init, close, executeScript });

  // 将原来的const ref变量封装到state中
  const state = reactive({
    config: null,
    file: null,
  });

  async function init(file, type = 'edit') {
    state.file = file;
    if (type === 'edit') {
      onlyOfficeConfig({
        fileId: file.fileId,
        fileName: file.name,
      }).then(res => {
        state.config = res.data;
      });
    } else if (type === 'stamp') {
      state.config = {
        width: '100%',
        height: '100%',
        type: 'desktop', // 或 'mobile'
        document: {
          fileType: file.fileExtension, // docx/xlsx...
          key: file.fileId.replace(',', '-'), // 唯一标识
          title: file.name,
          url: 'http://************:3100/dev' + file.url,
          permissions: {
            edit: true,
            download: true,
            print: true,
          },
        },
        macros: {
          enabled: true, // 启用宏功能
          security: "low" // 安全级别：low（允许所有宏）/ medium（提示）/ high（禁用）
        },
        editorConfig: {
          callbackUrl: 'http://************:30000/api/onlyoffice/callback',
          mode: 'edit', // coedit/view/comment
          lang: 'zh',
          user: { id: 'u2', name: '李四' },
        },
        events: {
          onAppReady: function (event) {
            console.log('OnlyOffice is ready!', event);
          },
          onDocumentReady: function (event) {
            console.log('Document is ready!', event);
          },
        },
      };
    }
  }

  // 插入图片的核心函数
  function executeScript(script) {
    const docEditor = new window.DocsAPI.DocEditor("placeholder", state.config);
    console.log('onlyofficeExec', script, docEditor);
    docEditor.executeMethod("executeScript", [script]);
  }

  function close() {
    state.config = null;
    state.file = null;
  }
</script>
