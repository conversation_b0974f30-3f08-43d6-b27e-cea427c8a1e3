<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" v-bind="getTableBindValue" ref="tableRef" @columns-change="handleColumnChange">
          <!-- 主表主体 -->
          <template #bodyCell="{ column = {}, record, index }">
            <slot name="bodyCell" :column="column" :record="record" :index="index"></slot>
            <!-- 序号列 -->
            <template v-if="column.flag === 'INDEX'">
              <div class="edit-row-action" v-if="columnData.type === 4 && !config.enableFlow">
                <span class="edit-row-index">{{ index + 1 }}</span>
                <i class="ym-custom ym-custom-arrow-expand" @click="handleRowForm(record)"></i>
              </div>
              <span v-else>{{ index + 1 }}</span>
            </template>
            <!-- 主表列渲染 -->
            <template v-else-if="column.key === 'f_review_code'">
              <p class="link-text" @click="handleReviewClick(record)">{{ record[column.prop] }}</p>
            </template>
            <template v-else-if="column.key.startsWith('f_')">
              {{ record[column.prop] }}
            </template>
            <template v-else-if="column.key === 'flowState' && config.enableFlow == 1 && (!record.top || columnData.type == 5)">
              <PzTag :content="getFlowStatusContent(record.flowState)" :color="getFlowStatusColor(record.flowState)" />
            </template>
            <template v-else-if="column.key === 'currentNodeName' && config.enableFlow == 1 && (!record.top || columnData.type == 5)">
              {{ record.currentNodeName }}
            </template>
            <template v-else-if="column.key === 'action' && (!record.top || columnData.type == 5)">
              <TableAction :actions="getTableActions(record, index)" :dropDownActions="getDropDownActions(record, index)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>

  <FlowParserModal :title="flowTitle" @register="registerFlowParserModal" @reload="reload" @close="" width="1000px" />
</template>

<script lang="ts" setup>
import {
  getModelList,
  getViewList,
} from '@/api/onlineDev/visualDev';
import { getDataInterfaceRes } from '@/api/systemData/dataInterface';
import { getOrgByOrganizeCondition, getDepartmentSelectAsyncList } from '@/api/permission/organize';
import { ref, reactive, onMounted, toRefs, computed, unref, nextTick, toRaw, provide } from 'vue';
import { useI18n } from '@/hooks/web/useI18n';
import { useUserStore } from '@/store/modules/user';
import { useBaseStore } from '@/store/modules/base';
import { usePopup } from '@/components/Popup';
import { TreeActionType } from '@/components/Tree';
import { useForm } from '@/components/Form';
import { BasicTable, useTable, TableAction, ActionItem, TableActionType, SorterResult } from '@/components/Table';
import { useRoute } from 'vue-router';
import { getScriptFunc, onlineUtils, getTimeUnit, getParamList } from '@/utils/jnpf';
import { getSearchFormSchemas } from '@/components/FormGenerator/src/helper/transform';
import { dyOptionsList } from '@/components/FormGenerator/src/helper/config';
import { cloneDeep } from 'lodash-es';
import dayjs from 'dayjs';
import { usePermission } from '@/hooks/web/usePermission';
import { useDefineSetting } from '@/hooks/setting/useDefineSetting';
import PzTag from '@/components/Pz/PzTag/index.vue'
import { useModal } from '@/components/Modal';
import FlowParserModal from "@/views/workFlow/components/FlowParserModal.vue";

const [registerFlowParserModal, { openModal: openFlowParserModal }] = useModal();

interface State {
  config: any;
  columnData: any;
  formConf: any;
  headerBtnsList: any[];
  columnBtnsList: any[];
  customHeaderBtnsList: any[];
  customColumnBtnsList: any[];
  columnOptions: any[];
  treeFieldNames: any;
  leftTreeData: any[];
  leftTreeLoading: boolean;
  isLeftTreeLazy: boolean;
  treeActiveId: string;
  treeActiveNodePath: any;
  columns: any[];
  complexColumns: any[];
  childColumnList: any[];
  exportList: any[];
  cacheList: any[];
  currRow: any;
  workFlowFormData: any;
  expandObj: any;
  columnSettingList: any[];
  searchSchemas: any[];
  treeRelationObj: any;
  treeQueryJson: any;
  leftTreeActiveInfo: any;
  customRow: any;
  customCell: any;
  tabActiveKey: any;
  tabList: any[];
  tabQueryJson: any;
  viewList: any[];
  currentView: any;
  flowTitle: string;
}

const props = defineProps(['config', 'modelId', 'menuId', 'isPreview', 'isDataManage', 'noSearch', 'noToolbar', 'hideColumns']);
const route = useRoute();
const { hasBtnP } = usePermission();
const { t } = useI18n();
const userStore = useUserStore();
const baseStore = useBaseStore();
const [registerFlowParser, { openPopup: openFlowParser }] = usePopup();
const leftTreeRef = ref<Nullable<TreeActionType>>(null);
const formRef = ref<any>(null);
const tableRef = ref<Nullable<TableActionType>>(null);
const detailRef = ref<any>(null);
const searchInfo = reactive({
  modelId: '',
  menuId: '',
  queryJson: '',
  superQueryJson: '',
  extraQueryJson: '',
});
const state = reactive<State>({
  config: {},
  columnData: {},
  formConf: {},
  headerBtnsList: [],
  columnBtnsList: [],
  customHeaderBtnsList: [],
  customColumnBtnsList: [],
  columnOptions: [],
  treeFieldNames: {
    children: 'children',
    title: 'fullName',
    key: 'id',
    isLeaf: 'isLeaf',
  },
  leftTreeData: [],
  leftTreeLoading: false,
  isLeftTreeLazy: false,
  treeActiveId: '',
  treeActiveNodePath: [],
  columns: [],
  complexColumns: [], // 复杂表头
  childColumnList: [],
  exportList: [],
  cacheList: [],
  currRow: {},
  workFlowFormData: {},
  expandObj: {},
  columnSettingList: [],
  searchSchemas: [],
  treeRelationObj: null,
  treeQueryJson: {},
  leftTreeActiveInfo: {},
  customRow: null,
  customCell: null,
  tabActiveKey: '',
  tabList: [],
  tabQueryJson: {},
  viewList: [],
  currentView: {},
  flowTitle: '',
});
const { columnData, flowTitle } = toRefs(state);
const { getFlowStatusContent, getFlowStatusColor } = useDefineSetting();
const [registerSearchForm, { updateSchema, resetFields, submit: searchFormSubmit }] = useForm({
  baseColProps: { span: 6 },
  showActionButtonGroup: true,
  showAdvancedButton: true,
  compact: true,
});
const [
  registerTable,
  {
    reload,
    setLoading,
  },
] = useTable({
  api: getModelList,
  immediate: false,
  clickToRowSelect: false,
  tableSetting: { setting: false, redo: !props.isPreview },
  afterFetch: data => {
    // 行内编辑
    if (state.columnData.type === 4) {
      const list = data.map(o => ({ ...o, rowEdit: false }));
      state.cacheList = cloneDeep(list);
      nextTick(() => {
        if (state.columnData.funcs?.afterOnload) setTableLoadFunc();
      });
      return list;
    }
    let list = data.map(o => ({
      ...o,
      ...state.expandObj,
    }));
    state.cacheList = cloneDeep(list);
    // 分组表格
    if (state.columnData.type === 3) {
      list.map(o => {
        if (o.children && o.children.length) {
          o.children = o.children.map(e => ({
            ...e,
            ...state.expandObj,
          }));
        }
      });
    }
    nextTick(() => {
      if (state.columnData.funcs?.afterOnload) setTableLoadFunc();
    });
    return list;
  },
});

provide('getLeftTreeActiveInfo', () => state.leftTreeActiveInfo);

const getHasBatchBtn = computed(
  () =>
    state.columnData.type != 5 &&
    (state.headerBtnsList.some(o => ['batchRemove', 'batchPrint', 'download'].includes(o.value)) || state.customHeaderBtnsList.length),
);
const getPagination = computed(() => {
  if ([3, 5].includes(state.columnData.type) || !state.columnData.hasPage) return false;
  return { pageSize: state.columnData.pageSize };
});
const getChildTableStyle = computed(() => (state.columnData.type == 3 || state.columnData.type == 5 ? 1 : state.columnData.childTableStyle));
const getColumns = computed(() => {
  const columns = unref(getChildTableStyle) == 2 || state.columnData.type == 4 ? state.columns : state.complexColumns;
  return props.isDataManage ? columns : setListValue(state.currentView?.columnList, columns, 'prop');
});
const getSearchList = computed(() => {
  const allSearchSchemas = cloneDeep(state.searchSchemas).map(o => ({ ...o, show: true }));
  const searchSchemas = setListValue(state.currentView?.searchList, allSearchSchemas, 'field');
  buildSearchOptions(searchSchemas);
  return props.isDataManage ? allSearchSchemas : searchSchemas;
});
const getRowKey = computed(() => (state.config.webType == 4 && state.columnData.viewKey ? state.columnData.viewKey : 'id'));
const getTableBindValue = computed(() => {
  let columns = unref(getColumns);
  if (state.config.enableFlow) {
    const boo = columns.some(o => o.dataIndex === 'flowState');
    if (!boo) {
      columns.push({
        title: '流程节点', dataIndex: "currentNodeName", width: 100, align: "center", fixed: columns.some(o => o.fixed == "right") ? "right" : "none"
      });
      columns.push({
        title: '流程状态', dataIndex: "flowState", width: 100, align: "center", fixed: columns.some(o => o.fixed == "right") ? "right" : "none"
      });
    }
  }
  if (props.hideColumns) {
    columns = columns.filter(o => !props.hideColumns.includes(o.dataIndex));
  }
  const defaultSortConfig = (state.columnData.defaultSortConfig || []).map(o => (o.sort === 'desc' ? '-' : '') + o.field);
  const data: any = {
    pagination: unref(getPagination),
    searchInfo: unref(searchInfo),
    defSort: { sidx: defaultSortConfig.join(',') },
    sortFn: (sortInfo: SorterResult | SorterResult[]) => {
      if (Array.isArray(sortInfo)) {
        const sortList = sortInfo.map(o => (o.order === 'descend' ? '-' : '') + o.field);
        return { sidx: sortList.join(',') };
      } else {
        const { field, order } = sortInfo;
        if (field && order) {
          // 排序字段
          return { sidx: (order === 'descend' ? '-' : '') + field };
        } else {
          return {};
        }
      }
    },
    columns,
    clearSelectOnPageChange: true,
    ellipsis: !!state.columnData.showOverflow,
    isTreeTable: [3, 5].includes(state.columnData.type),
    bordered: (unref(getChildTableStyle) != 2 && !!state.childColumnList?.length) || !!state.columnData.complexHeaderList?.length,
    rowKey: unref(getRowKey),
  };
  if (unref(getHasBatchBtn)) {
    const rowSelection: any = { type: 'checkbox' };
    if (state.columnData.type === 3) rowSelection.getCheckboxProps = record => ({ disabled: !!record.top });
    data.rowSelection = rowSelection;
  }
  if (state.columnBtnsList.length || state.customColumnBtnsList.length) {
    let customWidth = state.customColumnBtnsList.length ? 50 : 0;
    if (state.columnData.type == 4 && state.config.enableFlow) customWidth += 50;
    let columnBtnsLen = state.columnBtnsList.length;
    const actionWidth = columnBtnsLen ? columnBtnsLen * 50 + customWidth : customWidth + 20;
    data.actionColumn = {
      width: actionWidth,
      title: t('component.table.action'),
      dataIndex: 'action',
      fixed: 'right',
    };
  }
  if (state.customRow) data.customRow = state.customRow;
  return data;
});

function getTableActions(record, index): ActionItem[] {
  const list = state.columnBtnsList.map(o => {
    const item: ActionItem = {
      label: o.labelI18nCode ? t(o.labelI18nCode, o.label) : o.label,
      onClick: columnBtnsHandle.bind(null, o.value, record),
    };
    if (o.value === 'remove') item.color = 'error';
    if (state.config.enableFlow) {
      if (o.value === 'edit') item.disabled = ![0, 8, 9].includes(record.flowState);
      if (o.value === 'remove') item.disabled = ![0, 9].includes(record.flowState);
      if (o.value === 'detail') item.disabled = !record.flowState;
    } else {
      if (o?.event?.enableFunc) {
        const parameter = { row: record, rowIndex: index, onlineUtils };
        const func: any = getScriptFunc(o.event.enableFunc);
        item.disabled = (func && !func(parameter)) || false;
      }
    }
    return item;
  });
  if (record.rowEdit) {
    let editBtnList: ActionItem[] = [
      { label: t('common.saveText'), onClick: saveForRowEdit.bind(null, record, 0) },
      { label: t('common.cancelText'), color: 'error', onClick: cancelRowEdit.bind(null, record, index) },
    ];
    if (state.config.enableFlow) {
      editBtnList.push({ label: t('common.submitText'), onClick: submitForRowEdit.bind(null, record) });
    }
    return editBtnList;
  }
  return list;
}
function getDropDownActions(record, index): ActionItem[] {
  const list = state.customColumnBtnsList.map(o => {
    const item: ActionItem = {
      label: o.labelI18nCode ? t(o.labelI18nCode, o.label) : o.label,
      onClick: customBtnsHandle.bind(null, o, record, index),
    };
    if (o?.event?.enableFunc) {
      const parameter = { row: record, rowIndex: index, onlineUtils };
      const func: any = getScriptFunc(o.event?.enableFunc);
      item.disabled = (func && !func(parameter)) || false;
    }
    return item;
  });
  return list;
}
async function handleReviewClick(record) {
  const query = { paramList: [{ field: 'flow_task_id', defaultValue: record.id }] };
  const res = await getDataInterfaceRes('729732517992602949', query);
  state.flowTitle = '任务复核详情';
  const data = {
    id: record.id,
    flowId: record.f_flow_id,
    opType: 2,
    isFlow: 1,
    operatorId: res.data[0]?.f_id,
  };
  openFlowParserModal(true, data);
}
function init() {
  state.config = {
    modelId: props.modelId,
    menuId: props.menuId,
    isPreview: props.isPreview,
    ...props.config,
  };
  searchInfo.modelId = props.modelId;
  searchInfo.menuId = props.menuId ? props.menuId : route.meta.modelId as string;
  searchInfo.extraQueryJson = state.config.extraQueryJson;
  if (props.isPreview) searchInfo.menuId = '270579315303777093';
  if (!state.config.columnData || (state.config.webType != '4' && !state.config.formData)) return;
  state.columnData = JSON.parse(state.config.columnData);
  if (state.columnData.type === 3) {
    state.columnData.columnList = state.columnData.columnList.filter(o => o.prop != state.columnData.groupField);
  }
  state.formConf = state.config.formData ? JSON.parse(state.config.formData) : {};
  const customBtnsList = state.columnData.customBtnsList || [];
  const columnBtnsList = state.columnData.columnBtnsList || [];
  getHeaderBtnsList(state.columnData.btnsList || []);
  getColumnBtnsList(columnBtnsList, customBtnsList);
  state.columnOptions = state.columnData.columnOptions || [];
  if (!unref(getPagination)) (searchInfo as any).pageSize = 1000000;
  setLoading(true);
  if (state.columnData.funcs.rowStyle) {
    state.customRow = (record, index) => {
      const data = { row: record, rowIndex: index };
      const func: any = getScriptFunc(state.columnData.funcs.rowStyle);
      const style: any = func ? func(data) : null;
      if (!style) return {};
      return { style };
    };
  }
  if (state.columnData.funcs.cellStyle) {
    state.customCell = (record, rowIndex, column) => {
      const data = { row: record, rowIndex, column, columnIndex: column.key };
      const func: any = getScriptFunc(state.columnData.funcs.cellStyle);
      const style: any = func ? func(data) : null;
      if (!style) return {};
      return { style };
    };
  }
  getSearchSchemas();
  getColumnList();
  !props.isDataManage && initViewList();
  if (state.columnData.type == 4) buildOptions();
  if ([1, 4].includes(state.columnData.type)) getTabList();
  if (props.isPreview) return setLoading(false);
  if (state.columnData.type === 2) {
    state.treeFieldNames.key = state.columnData.treePropsValue || 'id';
    state.treeFieldNames.title = state.columnData.treePropsLabel || 'fullName';
    state.treeFieldNames.children = state.columnData.treePropsChildren || 'children';
    getTreeView();
  } else {
    nextTick(() => {
      unref(getSearchList)?.length ? searchFormSubmit() : reload({ page: 1 });
    });
  }
}
async function getTreeView() {
  state.isLeftTreeLazy = false;
  state.leftTreeLoading = true;
  state.leftTreeActiveInfo = {};
  state.treeQueryJson = {};
  state.leftTreeData = [];
  state.treeActiveId = '';
  state.treeActiveNodePath = [];
  let leftTreeData: any[] = [];
  if (state.columnData.treeDataSource === 'dictionary') {
    if (!state.columnData.treeDictionary) return (state.leftTreeLoading = false);
    leftTreeData = await baseStore.getDicDataSelector(state.columnData.treeDictionary);
  }
  if (state.columnData.treeDataSource === 'organize') {
    state.isLeftTreeLazy = true;
    const res = await getDepartmentSelectAsyncList();
    leftTreeData = res.data.list;
  }
  if (state.columnData.treeDataSource === 'api') {
    if (!state.columnData.treePropsUrl) return (state.leftTreeLoading = false);
    const query = { paramList: getParamList(state.columnData.treeTemplateJson) || [] };
    const res = await getDataInterfaceRes(state.columnData.treePropsUrl, query);
    leftTreeData = Array.isArray(res.data) ? res.data : [];
  }
  if (state.columnData.treeDataSource === 'formField') {
    const treeRelationObj: any = state.treeRelationObj;
    const jnpfKey = treeRelationObj?.__config__?.jnpfKey || '';
    if (state.columnData.treeRelation && ['organizeSelect', 'depSelect'].includes(jnpfKey)) {
      if (treeRelationObj.selectType === 'all') {
        state.isLeftTreeLazy = true;
        const res = await getDepartmentSelectAsyncList();
        leftTreeData = res.data.list;
      }
      if (treeRelationObj.selectType === 'custom' && treeRelationObj.ableIds?.length) {
        const departIds = jnpfKey === 'organizeSelect' ? treeRelationObj.ableIds.map(o => o[o.length - 1]) : treeRelationObj.ableIds;
        const res = await getOrgByOrganizeCondition({ departIds });
        leftTreeData = res.data.list;
      }
    }
  }
  state.leftTreeData = leftTreeData;
  state.leftTreeLoading = false;
  nextTick(() => {
    if (state.leftTreeData.length) leftTreeRef.value?.setExpandedKeys([state.leftTreeData[0].id]);
    unref(getSearchList)?.length ? searchFormSubmit() : reload({ page: 1 });
  });
}
//获取标签面板数据、设置标签面板默认值
async function getTabList() {
  state.tabList = [];
  if (!state.columnData.tabConfig || !state.columnData.tabConfig.on || !state.columnData.tabConfig.relationField) return;
  state.columnData.tabConfig?.hasAllTab && state.tabList.push({ fullName: '全部', id: '' });
  const list: any[] = state.columnData.columnOptions.filter(o => o.__vModel__ == state.columnData.tabConfig.relationField) || [];
  if (list?.length) {
    if (list[0].__config__.dataType == 'dictionary' && list[0].__config__.dictionaryType) {
      const data = (await baseStore.getDicDataSelector(list[0].__config__.dictionaryType)) || [];
      const options = list[0].props.value == 'enCode' ? data.map(o => ({ ...o, id: o.enCode })) : data;
      state.tabList = [...state.tabList, ...options];
    } else {
      state.tabList = [...state.tabList, ...list[0].options];
    }
  }
  if (state.tabList?.length) {
    state.tabActiveKey = state.tabList[0].id || '';
    state.tabQueryJson = { [state.columnData.tabConfig.relationField]: state.tabList[0].id };
  }
}
function getHeaderBtnsList(btnsList) {
  btnsList = btnsList.filter(o => o.show || !Reflect.has(o, 'show'));
  if (props.isPreview || props.isDataManage || !state.columnData.useBtnPermission) return (state.headerBtnsList = btnsList);
  // 过滤权限
  let btns: any[] = [];
  for (let i = 0; i < btnsList.length; i++) {
    if (hasBtnP('btn_' + btnsList[i].value)) btns.push(btnsList[i]);
  }
  state.headerBtnsList = btns;
}
function getColumnBtnsList(columnBtnsList, customBtnsList) {
  columnBtnsList = columnBtnsList.filter(o => o.show || !Reflect.has(o, 'show'));
  let btns: any[] = [];
  let customBtns: any[] = [];
  if (props.isPreview || props.isDataManage || !state.columnData.useBtnPermission) {
    btns = columnBtnsList;
    customBtns = customBtnsList;
  } else {
    // 过滤权限
    const permissionList = userStore.getPermissionList;
    const list = permissionList.filter(o => o.modelId === searchInfo.menuId);
    const perBtnList = list[0] && list[0].button ? list[0].button : [];
    for (let i = 0; i < columnBtnsList.length; i++) {
      inner: for (let j = 0; j < perBtnList.length; j++) {
        if ('btn_' + columnBtnsList[i].value === perBtnList[j].enCode) {
          btns.push(columnBtnsList[i]);
          break inner;
        }
      }
    }
    for (let i = 0; i < customBtnsList.length; i++) {
      inner: for (let j = 0; j < perBtnList.length; j++) {
        if (customBtnsList[i].value === perBtnList[j].enCode) {
          customBtns.push(customBtnsList[i]);
          break inner;
        }
      }
    }
  }
  state.columnBtnsList = btns;
  state.customHeaderBtnsList = customBtns.filter(o => o.event?.position === 2);
  state.customColumnBtnsList = customBtns.filter(o => o.event?.position !== 2);
  if (state.columnData.type == 5) state.customHeaderBtnsList = [];
}
function getSearchSchemas() {
  if (state.columnData.treeRelation) {
    for (let i = 0; i < state.columnData.columnOptions.length; i++) {
      const e = state.columnData.columnOptions[i];
      if (e.id === state.columnData.treeRelation) {
        state.treeRelationObj = { ...e, searchMultiple: false, jnpfKey: e.__config__.jnpfKey };
        break;
      }
    }
  }
  state.searchSchemas = getSearchFormSchemas(state.columnData.searchList);
}
function buildSearchOptions(searchSchemas) {
  searchSchemas.forEach(cur => {
    const config = cur.__config__;
    if (dyOptionsList.includes(config.jnpfKey)) {
      if (config.dataType === 'dictionary' && config.dictionaryType) {
        baseStore.getDicDataSelector(config.dictionaryType).then(res => {
          updateSchema([{ field: cur.field, componentProps: { options: res } }]);
        });
      }
      if (config.dataType === 'dynamic' && config.propsUrl) {
        const query = { paramList: config.templateJson || [] };
        getDataInterfaceRes(config.propsUrl, query).then(res => {
          const data = Array.isArray(res.data) ? res.data : [];
          updateSchema([{ field: cur.field, componentProps: { options: data } }]);
        });
      }
    }
    if ((Array.isArray(cur.value) && cur.value.length) || cur.value || cur.value === 0 || cur.value === false) cur.defaultValue = cur.value;
  });
  return searchSchemas;
}
// 获取列
function getColumnList() {
  let columnList: any[] = [];
  if (props.isPreview || props.isDataManage || !state.columnData.useColumnPermission) {
    columnList = state.columnData.columnList;
  } else {
    // 过滤权限
    const permissionList = userStore.getPermissionList;
    const list = permissionList.filter(o => o.modelId === searchInfo.menuId);
    const perColumnList = list[0] && list[0].column ? list[0].column : [];
    for (let i = 0; i < state.columnData.columnList.length; i++) {
      inner: for (let j = 0; j < perColumnList.length; j++) {
        if (state.columnData.columnList[i].prop === perColumnList[j].enCode) {
          columnList.push(state.columnData.columnList[i]);
          break inner;
        }
      }
    }
  }
  let columns = columnList.map(o => ({
    ...o,
    placeholder: state.columnData.type == 4 && o.placeholderI18nCode ? t(o.placeholderI18nCode, o.placeholder) : o.placeholder,
    title: o.labelI18nCode ? t(o.labelI18nCode, o.label) : o.label,
    dataIndex: o.prop,
    align: o.align,
    fixed: o.fixed == 'none' ? false : o.fixed,
    sorter: o.sortable ? { multiple: 1 } : o.sortable,
    width: o.width || 100,
    customCell: state.customCell || null,
  }));
  if (state.columnData.type !== 3 && state.columnData.type !== 5) columns = getComplexColumns(columns);
  state.columns = columns.filter(o => o.prop.indexOf('-') < 0);
  if (state.columnData.type == 4) buildRowRelation();
  getChildComplexColumns(columns);
}
function getComplexColumns(columns) {
  let complexHeaderList: any[] = state.columnData.complexHeaderList || [];
  if (!complexHeaderList.length) return columns;
  let childColumns: any[] = [];
  let firstChildColumns: string[] = [];
  for (let i = 0; i < complexHeaderList.length; i++) {
    const e = complexHeaderList[i];
    e.label = e.fullName;
    e.labelI18nCode = e.fullNameI18nCode;
    e.title = e.fullNameI18nCode ? t(e.fullNameI18nCode, e.fullName) : e.fullName;
    e.align = e.align;
    e.dataIndex = e.id;
    e.prop = e.id;
    e.children = [];
    e.jnpfKey = 'complexHeader';
    if (e.childColumns?.length) {
      childColumns.push(...e.childColumns);
      for (let k = 0; k < e.childColumns.length; k++) {
        const item = e.childColumns[k];
        for (let j = 0; j < columns.length; j++) {
          const o = columns[j];
          if (o.prop == item && o.fixed !== 'left' && o.fixed !== 'right') e.children.push({ ...o });
        }
      }
    }
    if (e.children.length) firstChildColumns.push(e.children[0].prop);
  }
  complexHeaderList = complexHeaderList.filter(o => o.children.length);
  let list: any[] = [];
  for (let i = 0; i < columns.length; i++) {
    const e = columns[i];
    if (!childColumns.includes(e.prop) || e.fixed === 'left' || e.fixed === 'right') {
      list.push(e);
    } else {
      if (firstChildColumns.includes(e.prop)) {
        const item = complexHeaderList.find(o => o.childColumns.includes(e.prop));
        list.push(item);
      }
    }
  }
  return list;
}
function getChildComplexColumns(columnList) {
  let list: any[] = [];
  for (let i = 0; i < columnList.length; i++) {
    const e = columnList[i];
    if (!e.prop.includes('-')) {
      list.push(e);
    } else {
      let prop = e.prop.split('-')[0];
      let vModel = e.prop.split('-')[1];
      let label = e.label.split('-')[0];
      let childLabel = e.label.replace(label + '-', '');
      if (e.fullNameI18nCode && Array.isArray(e.fullNameI18nCode) && e.fullNameI18nCode[0]) label = t(e.fullNameI18nCode[0], label);
      let newItem = {
        align: 'center',
        jnpfKey: 'table',
        prop,
        label,
        title: label,
        dataIndex: prop,
        children: [],
        customCell: state.customCell || null,
      };
      e.dataIndex = vModel;
      e.title = e.labelI18nCode ? t(e.labelI18nCode, childLabel) : childLabel;
      if (!state.expandObj.hasOwnProperty(`${prop}Expand`)) state.expandObj[`${prop}Expand`] = false;
      if (!list.some(o => o.prop === prop)) list.push(newItem);
      for (let i = 0; i < list.length; i++) {
        if (list[i].prop === prop) {
          list[i].children.push(e);
          break;
        }
      }
    }
  }
  if (unref(getChildTableStyle) != 2) getMergeList(list);
  getExportList(list);
  state.complexColumns = list;
  state.childColumnList = list.filter(o => o.jnpfKey === 'table');
  // 子表分组展示宽度取100
  if (unref(getChildTableStyle) !== 2) {
    for (let i = 0; i < state.childColumnList.length; i++) {
      const e = state.childColumnList[i];
      if (e.children?.length) e.children = e.children.map(o => ({ ...o, width: 100 }));
    }
  }
}
function getMergeList(list) {
  list.forEach(item => {
    if (item.jnpfKey === 'table' && item.children && item.children.length) {
      item.children.forEach((child, index) => {
        if (index == 0) {
          child.customCell = (record, rowIndex, column) => ({
            ...(state.customCell ? state.customCell(record, rowIndex, column) : {}),
            ...{
              rowspan: 1,
              colspan: item.children.length,
              class: 'child-table-box',
            },
          });
        } else {
          child.customCell = () => ({
            rowspan: 0,
            colspan: 0,
          });
        }
      });
    }
  });
}
function getExportList(list) {
  let exportList: any[] = [];
  for (let i = 0; i < list.length; i++) {
    if (list[i].jnpfKey === 'table') {
      if (state.columnData.type != 4) exportList.push(...list[i].children);
    } else if (list[i].jnpfKey === 'complexHeader') {
      exportList.push(...list[i].children);
    } else {
      exportList.push(list[i]);
    }
  }
  state.exportList = exportList;
}
function setTableLoadFunc() {
  const parameter = { data: state.cacheList, tableRef: tableRef.value, onlineUtils };
  const func: any = getScriptFunc(state.columnData.funcs.afterOnload);
  if (!func) return;
  func(parameter);
}
function handleColumnChange(data) {
  state.columnSettingList = data;
}
// 高级查询
function handleRowForm(record) {
  let fields: any[] = [];
  for (let i = 0; i < unref(getColumns).length; i++) {
    const e = unref(getColumns)[i];
    if (e.children?.length) {
      for (let j = 0; j < e.children.length; j++) {
        const o = e.children[j];
        o.__config__.span = 24;
        o.__config__.label = o.label;
        fields.push(toRaw(o));
      }
    } else {
      e.__config__.span = 24;
      e.__config__.label = e.label;
      fields.push(toRaw(e));
    }
  }
  fields = fields.map(o => {
    if (o.__config__?.templateJson && o.__config__?.templateJson.length) {
      o.__config__.templateJson = o.__config__.templateJson.map(o => ({ ...o, relationField: '' }));
    }
    if (o.templateJson && o.templateJson.length) {
      o.templateJson = o.templateJson.map(o => ({ ...o, relationField: '' }));
    }
    return o;
  });
  const formConf = { ...state.formConf, fields, popupType: 'general' };
  const data = {
    id: record.id,
    formConf,
    modelId: props.modelId,
    isPreview: props.isPreview,
    isDataManage: props.isDataManage,
    useFormPermission: state.columnData.useFormPermission,
    showMoreBtn: false,
    menuId: searchInfo.menuId,
    allList: state.cacheList,
    formData: record,
  };
  formRef.value?.init(data);
}
// 行内编辑获取选项
function buildOptions() {
  const loop = list => {
    for (let i = 0; i < list.length; i++) {
      const cur = list[i];
      if (cur.children?.length) loop(cur.children);
      const config = cur.__config__;
      if (!config) continue;
      if (dyOptionsList.includes(config.jnpfKey)) {
        if (config.dataType === 'dictionary' && config.dictionaryType) {
          cur.options = [];
          baseStore.getDicDataSelector(config.dictionaryType).then(res => {
            cur.options = res;
          });
        }
        if (config.dataType === 'dynamic' && config.propsUrl) {
          cur.options = [];
          const query = { paramList: config.templateJson || [] };
          getDataInterfaceRes(config.propsUrl, query).then(res => {
            cur.options = Array.isArray(res.data) ? res.data : [];
          });
        }
      }
    }
  };
  loop(state.columns);
}
function buildRowRelation() {
  const loop = list => {
    for (let i = 0; i < list.length; i++) {
      let cur = list[i];
      if (cur.children?.length) loop(cur.children);
      const config = cur?.__config__;
      if (!config) continue;
      if (config.jnpfKey === 'datePicker') {
        if (config.startTimeRule) {
          if (config.startTimeType == 1) cur.startTime = config.startTimeValue;
          if (config.startTimeType == 3) cur.startTime = new Date().getTime();
          if (config.startTimeType == 4 || config.startTimeType == 5) {
            const type = getTimeUnit(config.startTimeTarget);
            const method = config.startTimeType == 4 ? 'subtract' : 'add';
            const startTime = dayjs()[method](config.startTimeValue, type);
            let realStartTime = startTime.startOf('day').valueOf();
            if (config.startTimeTarget == 4) realStartTime = startTime.startOf('minute').valueOf();
            if (config.startTimeTarget == 5) realStartTime = startTime.startOf('second').valueOf();
            if (config.startTimeTarget == 6) realStartTime = startTime.valueOf();
            cur.startTime = realStartTime;
          }
        }
        if (config.endTimeRule) {
          if (config.endTimeType == 1) cur.endTime = config.endTimeValue;
          if (config.endTimeType == 3) cur.endTime = new Date().getTime();
          if (config.endTimeType == 4 || config.endTimeType == 5) {
            const type = getTimeUnit(config.endTimeTarget);
            const method = config.endTimeType == 4 ? 'subtract' : 'add';
            const endTime = dayjs()[method](config.endTimeValue, type);
            let realEndTime = endTime.endOf('day').valueOf();
            if (config.endTimeTarget == 4) realEndTime = endTime.endOf('minute').valueOf();
            if (config.endTimeTarget == 5) realEndTime = endTime.endOf('second').valueOf();
            if (config.endTimeTarget == 6) realEndTime = endTime.valueOf();
            cur.endTime = realEndTime;
          }
        }
      }
      if (config.jnpfKey === 'timePicker') {
        if (config.startTimeRule) {
          if (config.startTimeType == 1) cur.startTime = config.startTimeValue || null;
          if (config.startTimeType == 3) cur.startTime = dayjs().format(cur.format);
          if (config.startTimeType == 4 || config.startTimeType == 5) {
            const type = getTimeUnit(config.startTimeTarget + 3);
            const method = config.startTimeType == 4 ? 'subtract' : 'add';
            const startTime = dayjs()[method](config.startTimeValue, type).format(cur.format);
            cur.startTime = startTime;
          }
        }
        if (config.endTimeRule) {
          if (config.endTimeType == 1) cur.endTime = config.endTimeValue || null;
          if (config.endTimeType == 3) cur.endTime = dayjs().format(cur.format);
          if (config.endTimeType == 4 || config.endTimeType == 5) {
            const type = getTimeUnit(config.endTimeTarget + 3);
            const method = config.endTimeType == 4 ? 'subtract' : 'add';
            const endTime = dayjs()[method](config.endTimeValue, type).format(cur.format);
            cur.endTime = endTime;
          }
        }
      }
    }
  };
  loop(state.columns);
}
function initViewList(currentId = '') {
  const query = {
    menuId: searchInfo.menuId,
  };
  getViewList(query).then(res => {
    const columns: any[] = unref(getChildTableStyle) == 2 || state.columnData.type == 4 ? state.columns : state.complexColumns;
    const searchList: any[] = state.searchSchemas.map(o => ({ label: o.label, id: o.field, show: o.show }));
    const columnList: any[] = columns.map(o => ({ label: o.label, id: o.prop, show: true, fixed: o.fixed || 'none', labelI18nCode: o.labelI18nCode }));
    state.viewList = (res.data || []).map(o => {
      if (o.type == 0) return { ...o, searchList, columnList };
      return { ...o, searchList: o.searchList ? JSON.parse(o.searchList) : [], columnList: o.columnList ? JSON.parse(o.columnList) : [] };
    });
    if (currentId) {
      state.currentView = state.viewList.filter(o => o.id === currentId)[0] || state.viewList[0];
    } else {
      state.currentView = state.viewList.filter(o => o.status === 1)[0] || state.viewList[0];
    }
  });
}
function setListValue(data: any[] = [], defaultData: any[] = [], key) {
  let list: any[] = [];
  for (let i = 0; i < data.length; i++) {
    for (let j = 0; j < defaultData.length; j++) {
      if (data[i].show && data[i].id == defaultData[j][key]) list.push(defaultData[j]);
    }
  }
  return list;
}

onMounted(() => {
  init();
});

// 暴露方法给父组件使用
defineExpose({
  reload
});
</script>
