<template>
  <a-modal
    v-model:open="visible"
    :footer="null"
    :closable="false"
    :keyboard="false"
    :maskClosable="false"
    class="common-container-modal jnpf-full-modal full-modal file-preview-modal"
    wrap-class-name="fullscreen-modal">
    <template #closeIcon>
      <ModalClose :canFullscreen="false" @cancel="handleCancel" />
    </template>
    <template #title>
      <div class="jnpf-full-modal-header">
        <div class="header-title">
          <p class="header-txt">{{ title }}</p>
        </div>
        <a-space class="options" :size="10">
          <a-button type="primary" @click="handleStamp()" v-if="showStamp && !isEditing">盖章归档</a-button>
          <a-button type="primary" @click="insertImageToDocument()" v-if="showStamp && isEditing">盖章</a-button>
          <a-button type="primary" @click="handleEdit()" v-if="showEdit && !isEditing">编辑</a-button>
<!--          <a-button type="primary" @click="handleDownload()" v-if="showDownload">下载</a-button>-->
          <a-button @click="handleCancel()">关闭</a-button>
        </a-space>
      </div>
    </template>
    <div class="basic-content bg-white" v-loading="loading">
      <iframe v-if="!isEditing" width="100%" height="100%" :src="url" frameborder="0"></iframe>
      <OfficeEditor v-else ref="officeEditor" />
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
  import { nextTick, reactive, ref, toRefs } from 'vue';
  import { Modal as AModal } from 'ant-design-vue';
  import ModalClose from '@/components/Modal/src/components/ModalClose.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getDownloadUrl, previewFile } from '@/api/basic/common';
  import { downloadByUrl } from '@/utils/file/download';
  import { getToken } from '@/utils/auth';
  import OfficeEditor from '@/components/Pz/OfficeEditor/index.vue';

  interface State {
    visible: boolean;
    loading: boolean;
    title: string;
    url: string;
    file: any;
    isEditing: boolean;
  }
  const props = defineProps({
    showDownload: { type: Boolean, default: false },
    showEdit: { type: Boolean, default: false },
    showStamp: { type: Boolean, default: false },
    type: { type: String, default: 'annex' },
  });
  defineEmits(['register']);
  const { createMessage } = useMessage();
  const { t } = useI18n();
  const state = reactive<State>({
    visible: false,
    loading: false,
    title: '',
    url: '',
    file: {},
    isEditing: false,
  });
  const officeEditor = ref<any>(null);
  const { visible, loading, title, url, isEditing } = toRefs(state);

  defineExpose({ init });

  function init(file) {
    state.title = '文档预览 - ' + file.name;
    state.url = '';
    state.file = file;
    state.visible = true;
    state.loading = true;

    let query = {
      fileName: file.fileId,
      fileVersionId: file.fileVersionId,
      fileDownloadUrl: file.url,
    };
    previewFile(query)
      .then(res => {
        state.loading = false;
        if (res.data) {
          state.url = res.data + '&token=' + getToken();
        } else {
          createMessage.warning('文件不存在');
          handleCancel();
        }
      })
      .catch(() => {
        state.loading = false;
        handleCancel();
      });
  }
  async function handleCancel() {
    if (state.isEditing) {
      officeEditor.value.close();
      state.isEditing = false;
    }
    state.visible = false;
  }

  function handleDownload() {
    if (!state.file.fileId) return;
    getDownloadUrl(props.type, state.file.fileId).then(res => {
      downloadByUrl({ url: res.data.url, fileName: state.file.name });
    });
  }

  // 添加编辑按钮点击事件
  async function handleEdit() {
    state.isEditing = true;
    state.title = '在线编辑 - ' + state.file.name;
    await nextTick();
    officeEditor.value.init(state.file, 'edit');
  }
  // 添加编辑按钮点击事件
  async function handleStamp() {
    state.isEditing = true;
    state.title = '盖章 - ' + state.file.name;
    await nextTick();
    officeEditor.value.init(state.file, 'stamp');
  }

  // 插入图片的核心函数
  function insertImageToDocument() {
    const script = `
        const doc = Api.GetDocument();
        const paragraph = doc.GetElement(0);

        const image = Api.CreateImage(
            "https://static.onlyoffice.com/assets/docs/samples/img/onlyoffice_logo.png",
            60 * 36000,
            35 * 36000
        );

        image.SetWrappingStyle("inFront");
        image.SetDistances(0, 0, 0, 0);

        paragraph.AddDrawing(image);
    `;
    officeEditor.value.executeScript(script);
  }

</script>
<style lang="less">
  .file-preview-modal {
    .ant-modal-body {
      padding: 10px !important;
    }
    .header-txt {
      max-width: 80vw !important;
    }
  }
</style>
