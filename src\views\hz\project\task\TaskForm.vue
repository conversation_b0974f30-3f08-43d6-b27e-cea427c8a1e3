<template>
  <BasicDrawer
    class="pz-style"
    v-bind="$attrs"
    @register="registerDrawer"
    :width="formConf.drawerWidth"
    showFooter
    :closable="false"
    :okText="getOkText"
    :cancelText="getCancelText"
    @ok="handleSubmit"
    :closeFunc="onClose">

    <Skeleton :loading="loading" active/>
    <template #title v-if="!loading">
      <div class="drawer-title-1 flex-gap-10">
        <span class="task-title">{{ state.config.record.f_custom_task_name }}</span>
        <PzTag size="small" :color="getTaskPriorityColor(state.formData.f_priority_jnpfId)">{{ state.formData.f_priority }}</PzTag>
        <a-progress :percent="state.config.record.f_progress?state.config.record.f_progress:0" :size="30" type="circle" :show-info="true" />
      </div>
      <div class="drawer-title-2">
        <span class="task-code">#{{ state.config.record.f_task_code }}</span>
      </div>
      <div class="drawer-title-3">
        <div class="flex-gap-10">
          <div class="task-person" v-if="state.config.record.f_task_user_id">{{ state.config.record.f_task_user_id }}</div>
          <div class="task-date" v-if="state.formData.f_creator_time">
            创建于<PzTag size="small" color="#1890ff">{{ state.formData.f_creator_time }}</PzTag>
          </div>
          <div class="task-date" v-if="state.formData.f_last_modify_time">
            更新于<PzTag size="small" color="#1890ff">{{ state.formData.f_last_modify_time }}</PzTag>
          </div>
        </div>
        <div class="flex-gap-10">
          <PzTag :color="getTaskStatusColor(state.config.record.f_task_status_jnpfId)">{{ state.config.record.f_task_status }}</PzTag>
          <PzTag v-if="state.formData.f_review_status_jnpfId!=0" :color="getTaskStatusColor(state.formData.f_review_status_jnpfId)">{{ state.formData.f_review_status }}</PzTag>
          <a-button v-if="state.config.record.f_task_status_jnpfId==3 && ['0','2'].includes(state.formData.f_review_status_jnpfId)" size="small" type="primary" @click="handleReviewClick">提交复核</a-button>
        </div>

      </div>
    </template>
    <template #insertFooter>
      <div class="float-left mt-5px" v-if="showContinueBtn">
        <JnpfCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <div class="p-10px" v-if="!loading">
      <Parser ref="parserRef" :formConf="formConf" @submit="submitForm" :key="key">
        <template #taskReviewList>
          <TaskReview />
        </template>
      </Parser>
    </div>
  </BasicDrawer>
  <FlowParserModal title="提交复核" @register="registerFlowParserModal" @reload="" @close="handleFlowParserModalClose" width="600px" />
  <AnalysisForm ref="analysisForm"/>

</template>
<script lang="ts" setup>
  import { createModel, updateModel, getModelInfo } from '@/api/onlineDev/visualDev';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject, provide } from 'vue';
  import Parser from '@/components/FormGenerator/src/components/Parser.vue';
  import { BasicDrawer, useDrawer } from '@/components/Drawer';
  import { useBaseStore } from '@/store/modules/base';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import { useGeneratorStore } from '@/store/modules/generator';
  import { cloneDeep } from 'lodash-es';
  import dayjs from 'dayjs';
  import { getDateTimeUnit } from '@/utils/jnpf';
  import PzTag from '@/components/Pz/PzTag/index.vue';
  import { getTaskStatusColor, getTaskPriorityColor } from '../../util';
  import { Skeleton } from 'ant-design-vue';
  import JnpfCheckboxSingle from '@/components/Jnpf/Checkbox/src/CheckboxSingle.vue';
  const [registerFlowParserModal, { openModal: openFlowParserModal }] = useModal();
  import FlowParserModal from "@/views/workFlow/components/FlowParserModal.vue";
  import { useModal } from "@/components/Modal";
  import AnalysisForm from "@/views/hz/project/task/AnalysisForm.vue";
  import TaskReview from "./taskReviewList/index.vue";

  interface State {
    formConf: any;
    defaultFormConf: any;
    formData: any;
    config: any;
    loading: boolean;
    prevBtnLoading: boolean;
    nextBtnLoading: boolean;
    key: number;
    dataForm: any;
    formOperates: any[];
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
    showFlowParserModal: boolean;
  }

  const emit = defineEmits(['reload', 'close']);
  const emitter : any = inject('emitter');
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const generatorStore = useGeneratorStore();
  const { createMessage } = useMessage();
  const { t } = useI18n();
  const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer();
  const parserRef = ref<any>(null);
  const analysisForm = ref<any>(null);
  const state = reactive<State>({
    formConf: {},
    defaultFormConf: {},
    formData: {},
    config: {},
    loading: true,
    prevBtnLoading: false,
    nextBtnLoading: false,
    key: +new Date(),
    dataForm: {
      id: '',
      data: '',
    },
    formOperates: [],
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
    showFlowParserModal: false,
  });
  const { formConf, key, loading, continueText, showContinueBtn, submitType, showFlowParserModal } = toRefs(state);

  const getOkText = computed(() => {
    const text = state.formConf.confirmButtonTextI18nCode
      ? t(state.formConf.confirmButtonTextI18nCode, state.formConf.confirmButtonText)
      : state.formConf.confirmButtonText;
    return text || t('common.okText');
  });
  const getCancelText = computed(() => {
    const text = state.formConf.cancelButtonTextI18nCode
      ? t(state.formConf.cancelButtonTextI18nCode, state.formConf.cancelButtonText)
      : state.formConf.cancelButtonText;
    return text || t('common.cancelText');
  });
  const baseStore = useBaseStore();

  defineExpose({ init });

  provide('openAnalysisForm', openAnalysisForm);
  function openAnalysisForm(fruitData :any) {
    analysisForm.value?.init(fruitData);
  }

  function fillFormData(form, data, isAdd) {
    const userInfo = userStore.getUserInfo;
    const currDate = new Date();
    const loop = (list, parent?) => {
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        if (item.__vModel__) {
          let val = data.hasOwnProperty(item.__vModel__) ? data[item.__vModel__] : item.__config__.defaultValue;
          if (!item.__config__.isSubTable) item.__config__.defaultValue = val;
          if (isAdd || item.__config__.isSubTable) {
            if (item.__config__.defaultCurrent) {
              if (item.__config__.jnpfKey === 'datePicker') {
                item.__config__.defaultValue = dayjs(currDate).startOf(getDateTimeUnit(item.format)).valueOf();
              }
              if (item.__config__.jnpfKey === 'timePicker') {
                item.__config__.defaultValue = dayjs(currDate).format(item.format || 'HH:mm:ss');
              }
              if (item.__config__.jnpfKey === 'organizeSelect' && userInfo.organizeIdList?.length) {
                item.__config__.defaultValue = item.multiple ? [userInfo.organizeIdList] : userInfo.organizeIdList;
              }
              if (item.__config__.jnpfKey === 'depSelect' && userInfo.departmentId) {
                item.__config__.defaultValue = item.multiple ? [userInfo.departmentId] : userInfo.departmentId;
              }
              if (item.__config__.jnpfKey === 'userSelect' && userInfo.userId) {
                item.__config__.defaultValue = item.multiple ? [userInfo.userId] : userInfo.userId;
              }
              if (item.__config__.jnpfKey === 'usersSelect' && userInfo.userId) {
                item.__config__.defaultValue = item.multiple ? [userInfo.userId + '--user'] : userInfo.userId + '--user';
              }
              if (item.__config__.jnpfKey === 'posSelect' && userInfo.positionIds?.length) {
                item.__config__.defaultValue = item.multiple ? userInfo.positionIds.map(o => o.id) : userInfo.positionIds[0].id;
              }
              if (item.__config__.jnpfKey === 'roleSelect' && userInfo.roleIds?.length) {
                item.__config__.defaultValue = item.multiple ? userInfo.roleIds : userInfo.roleIds[0];
              }
              if (item.__config__.jnpfKey === 'groupSelect' && userInfo.groupIds?.length) {
                item.__config__.defaultValue = item.multiple ? userInfo.groupIds : userInfo.groupIds[0];
              }
              if (item.__config__.jnpfKey === 'sign' && userInfo.signImg) {
                item.__config__.defaultValue = userInfo.signImg;
              }
            }
          }
          if (isAdd && !item.__config__.isSubTable && data.hasOwnProperty(item.__vModel__)) item.__config__.defaultValue = data[item.__vModel__];
          if (!state.config.isPreview && !state.config.isDataManage && state.config.useFormPermission) {
            let id = item.__config__.isSubTable ? parent.__vModel__ + '-' + item.__vModel__ : item.__vModel__;
            let noShow = true;
            if (state.formOperates && state.formOperates.length) {
              noShow = !state.formOperates.some(o => o.enCode === id);
            }
            noShow = item.__config__.noShow ? item.__config__.noShow : noShow;
            item.__config__.noShow = noShow;
          }
        }
        if (item.__config__ && item.__config__.children && Array.isArray(item.__config__.children)) {
          loop(item.__config__.children, item);
        }
      }
    };
    loop(form.fields);
    form.formData = data;

    baseStore.getDictionaryData('hzTaskPriority', '', state.formData.f_priority_jnpfId).then((res :any) => {
      state.formData.f_priority = res.fullName;
    });
    baseStore.getDictionaryData('hzTaskReviewStatus', '', state.formData.f_review_status_jnpfId).then((res :any) => {
      state.formData.f_review_status = res.fullName;
    });
  }

  function handleReviewClick() {
    const data = {
      id: '',
      flowId: '708235053422873477',
      opType: '-1',
      isFlow: 1,
    };
    localStorage.setItem('autoFillFormData', JSON.stringify({
      f_project_manager_id: localStorage.getItem('project_manager_id'),
      f_execution_manager_id: localStorage.getItem('execution_manager_id'),
      f_task_user_id: state.formData.f_task_user_id_jnpfId,
      f_project_code: state.formData.f_project_code_jnpfId,
      f_task_code: state.formData.f_task_code_jnpfId,
      f_review_level: state.formData.f_review_level_jnpfId,
    }))
    nextTick(() => {
      openFlowParserModal(true, data);
    });
  }
  function handleFlowParserModalClose() {
    state.showFlowParserModal = false;
    localStorage.removeItem('autoFillFormData');
  }

  async function init(data) {
    await baseStore.getDictionaryAll();
    state.loading = true;
    state.submitType = 0;
    state.isContinue = false;
    state.prevBtnLoading = false;
    state.nextBtnLoading = false;
    state.title = data.title ? data.title : (!data.id || data.id === 'jnpfAdd' ? t('common.add2Text') : t('common.editText'));
    state.continueText = !data.id ? t('common.continueAndAddText') : t('common.continueText');
    state.config = data;
    state.defaultFormConf = cloneDeep(data.formConf);
    state.formConf = cloneDeep(state.defaultFormConf);
    state.showContinueBtn = !data.formData && state.formConf.hasConfirmAndAddBtn;
    state.dataForm.id = !data.id || data.id === 'jnpfAdd' ? '' : data.id;
    getFormOperates();
    openForm();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    nextTick(() => {
      if (!data.formData) return setTimeout(initData, 0);
      // 行内编辑
      setTimeout(() => {
        state.formData = { ...data.formData, id: state.dataForm.id };
        setFormValue();
      }, 0);
    });
  }
  function initData() {
    changeLoading(true);
    state.loading = true;
    if (state.config.id) {
      const extra = { modelId: state.config.modelId, id: state.config.id, type: 2 };
      generatorStore.setDynamicModelExtra(extra);
      getInfo(state.config.id);
    } else {
      generatorStore.setDynamicModelExtra({});
      state.formData = {};
      setFormValue(true);
    }
  }
  function getInfo(id) {
    getModelInfo(state.config.modelId, id).then(res => {
      state.dataForm = res.data || {};
      if (!state.dataForm.data) return;
      state.formData = { ...JSON.parse(state.dataForm.data), id: state.dataForm.id };
      setFormValue();
    });
  }
  function setFormValue(isAdd = false) {
    if (isAdd && getLeftTreeActiveInfo) state.formData = { ...(getLeftTreeActiveInfo() || {}) };
    state.formConf = cloneDeep(state.defaultFormConf);
    fillFormData(state.formConf, state.formData, isAdd);
    nextTick(() => {
      state.key = +new Date();
      state.loading = false;
      state.prevBtnLoading = false;
      state.nextBtnLoading = false;
      changeLoading(false);
    });
  }
  function getFormOperates() {
    if (state.config.isPreview || state.config.isDataManage || !state.config.useFormPermission) return;
    const permissionList = userStore.getPermissionList;
    const modelId = state.config.menuId;
    const list = permissionList.filter(o => o.modelId === modelId);
    state.formOperates = list[0] && list[0].form ? list[0].form : [];
  }
  function submitForm(data, callback) {
    if (!data) return;
    setFormProps({ confirmLoading: true });
    const formData = { ...state.formData, ...data };
    state.dataForm.data = JSON.stringify(formData);
    const formMethod = state.dataForm.id ? updateModel : createModel;
    formMethod(state.config.modelId, state.dataForm)
      .then(res => {
        createMessage.success(res.msg);
        if (callback && typeof callback === 'function') callback();
        setFormProps({ confirmLoading: false });
        if (state.submitType == 1) {
          initData();
          state.isContinue = true;
        } else {
          setFormProps({ open: false });
          emit('reload');
          emitter.emit("reload");
        }
      })
      .catch(() => {
        setFormProps({ confirmLoading: false });
      });
  }
  function handleSubmit() {
    if (state.config.isPreview) return createMessage.warning('功能预览不支持数据保存');
    getParser().handleSubmit();
  }
  function getParser() {
    const parser = unref(parserRef);
    if (!parser) throw new Error('parser is null!');
    return parser;
  }
  function openForm() {
    if (state.formConf.popupType === 'drawer') return openDrawer();
  }
  function setFormProps(data) {
    if (state.formConf.popupType === 'drawer') return setDrawerProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }
  async function onClose() {
    if (state.isContinue) emit('reload');
    emit('close');
    return true;
  }
</script>
<style>
  .jnpf-basic-drawer.ant-drawer .pz-style.ant-drawer-content {
    .ant-drawer-header {
      height: unset;
      box-shadow: 0 0 20px 0 #EDEFF1;
      z-index: 1;
      background-color: white!important;

      .drawer-title-2,
      .drawer-title-3 {
        color: #727272;
      }

      .drawer-title-3 {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
      }

      .ant-drawer-title {
        font-size: 14px;
        font-weight: normal;
      }
      .task-title {
        font-size: 18px;
        font-weight: bold;
      }

    }

    .ant-drawer-body {
      padding: 10px 14px 0;
    }
    .flex-gap-10 {
      display: flex;
      gap: 10px;
      align-items: baseline;
    }
    .form-extra-panel {
      border-left: none;
    }
  }
</style>
