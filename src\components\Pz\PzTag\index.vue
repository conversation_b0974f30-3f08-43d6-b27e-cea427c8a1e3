<template>
  <span
    class="pz-tag"
    :class="{ 'is-border': border, [`pz-tag--${size}`]: size }"
    :style="tagStyle"
  >
    {{content}}<slot></slot>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  color?: string
  content?: string
  border?: boolean
  size?: 'small' | 'normal' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  color: '',
  border: false,
  size: 'normal',
  content: ''
})

const tagStyle = computed(() => {
  if (!props.color) return {}

  return {
    color: props.color,
    backgroundColor: `${props.color}26`, // 15% 透明度
    borderColor: props.border ? props.color : 'transparent'
  }
})
</script>

<style scoped>
.pz-tag {
  display: inline-block;
  padding: 6px 8px;
  font-size: 14px;
  line-height: 1;
  border: 1px solid transparent;
  border-radius: 5px;
  background-clip: padding-box;
}

.pz-tag.is-border {
  border-style: solid;
}

.pz-tag--small {
  padding: 4px 6px;
  font-size: 13px;
}

.pz-tag--large {
  padding: 8px 12px;
  font-size: 15px;
}
</style>
