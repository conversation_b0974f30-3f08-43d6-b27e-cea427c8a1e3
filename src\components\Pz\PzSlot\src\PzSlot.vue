<template>
  <div class="pz-slot-container">
    <div v-if="slotName && $slots[slotName]" class="pz-slot-content">
      <slot :name="slotName" :slotData="slotData"></slot>
    </div>
    <div v-else-if="slotName && !$slots[slotName]" class="pz-slot-placeholder">
      <jnpf-empty :description="`插槽 '${slotName}' 未定义`" />
    </div>
    <div v-else class="pz-slot-placeholder">
      <jnpf-empty description="未配置插槽名称" />
    </div>
  </div>
</template>

<script lang="ts" setup>

defineOptions({ name: 'JnpfPzSlot', inheritAttrs: false });

defineProps({
  slotName: { type: String, default: '' },
  slotData: { type: Object, default: () => ({}) },
});
</script>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-pz-slot';

.@{prefix-cls} {
  width: 100%;

  .pz-slot-container {
    width: 100%;
    min-height: 40px;

    .pz-slot-content {
      width: 100%;
    }

    .pz-slot-placeholder {
      width: 100%;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      background-color: #fafafa;
      color: #999;
    }
  }
}
</style>
