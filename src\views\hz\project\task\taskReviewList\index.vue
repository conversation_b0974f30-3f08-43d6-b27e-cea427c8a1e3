<template>
  <div v-if="config" class="embed-form-container">
    <TaskReviewList class="sublist"
          :modelId="modelId" :menuId="menuId"
          :config="config"
          :noSearch="true" :noToolbar="true"
          :hideColumns="['f_task_code', 'f_task_user_id', 'f_review_level']" />
  </div>
  <div v-else class="embed-form-placeholder">
    <jnpf-empty description="正在加载" />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, inject } from "vue";
  import { getConfigData } from '@/api/onlineDev/visualDev';
  import { createAsyncComponent } from '@/utils/factory/createAsyncComponent';
  const TaskReviewList = createAsyncComponent(() => import('./TaskReviewList.vue'))
  defineOptions({ name: 'TaskReview', inheritAttrs: false });

  const menuId = '705771608328899205';
  const modelId = '708036171288021893';
  const config :any = ref(null);
  const parameter :any = inject('parameter');

  function init() {
    getConfigData(modelId).then(res => {
      config.value = res.data;
      if (!config.value) return;
      config.value.id = config.value.id || modelId;
      config.value.extraQueryJson = JSON.stringify({ f_task_code: parameter?.formData?.f_task_code });
      config.value.enableFlow = true;
    });
  }

  onMounted(() => {
    init();
  });
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-embed-form';

  .@{prefix-cls} {
    width: 100%;

    .embed-form-container {
      width: 100%;
      height: 100%;

      .embed-form-iframe {
        width: 100%;
        height: var(--height);
        border: var(--borderWidth) var(--borderType) var(--borderColor);
        border-radius: 4px;
      }
    }

    .embed-form-placeholder {
      width: 100%;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      background-color: #fafafa;
    }
  }
</style>
